package at.derneubauer.backend.rest.smartbricks

import at.derneubauer.backend.config.SmartbricksProperties
import at.derneubauer.backend.service.SmartbricksService
import jakarta.servlet.http.HttpServletRequest
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.web.DefaultOAuth2AuthorizationRequestResolver
import org.springframework.security.oauth2.core.endpoint.PkceParameterNames
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import org.springframework.web.util.UriComponentsBuilder
import java.net.URL
import java.security.MessageDigest
import java.security.SecureRandom
import java.util.Base64

@Controller
@RequestMapping("/admin/smartbricks")
class SmartbricksController(
    private val smartbricksProperties: SmartbricksProperties,
    private val clientRegistrationRepository: ClientRegistrationRepository,
    private val smartbricksService: SmartbricksService,
) {
    companion object {
        private val log = LoggerFactory.getLogger(this::class.java)
    }

    @GetMapping("/authorization-page")
    fun viewSmartbricksAuthorizationPage(): String {
        return "smartbricks/authorization-page"
    }

    @GetMapping("/webview")
    fun getSmartbricksWebViewPage(
        model: Model,
        request: HttpServletRequest,
        @RequestParam("smartbricksProjectId") smartbricksProjectId: Long,
    ): String {
        val clientRegistration = clientRegistrationRepository.findByRegistrationId(
            smartbricksProperties.clientRegistrationId,
        )

        if (clientRegistration != null) {
            val resolver = DefaultOAuth2AuthorizationRequestResolver(
                clientRegistrationRepository,
                "/oauth2/authorization",
            )
            val authorizationRequest = resolver.resolve(request, clientRegistration.registrationId)

            if (authorizationRequest != null) {
                /**
                 * Secure random state to prevent CSRF attacks.
                 * We sent it with our request and the IDP sends it back to us so
                 * we can verify that the request is coming from the IDP and not a malicious actor.
                 */
                val state = generateSecureRandomString()

                /**
                 * PKCE (Proof Key for Code Exchange) is a security best practice to prevent authorization code i
                 * injection attacks. In this scenario we never actually exchange the code for a token but if someone
                 * hijacked the authorization_code they could do so. With PKCE they would also have to guess
                 * the code_verifier since we told the IDP to only grant tokens for users that can pass the challenge.
                 */
                val codeVerifier = generateSecureRandomString()
                val codeChallenge = generatePKCEChallenge(codeVerifier)

                val authorizationUrl = UriComponentsBuilder
                    .fromUriString(clientRegistration.providerDetails.authorizationUri)
                    .queryParam("response_type", "code")
                    .queryParam("client_id", clientRegistration.clientId)
                    .queryParam("scope", clientRegistration.scopes.joinToString(" "))
                    .queryParam("prompt", "none") // silent authorization
                    .queryParam(
                        "response_mode",
                        "web_message"
                    ) // return the response in a postMessage event we can handle in JS
                    .queryParam("redirect_uri", authorizationRequest.redirectUri)
                    .queryParam("state", state)
                    .queryParam(PkceParameterNames.CODE_CHALLENGE, codeChallenge)
                    .queryParam(PkceParameterNames.CODE_CHALLENGE_METHOD, "S256")
                    .toUriString()

                model.addAttribute(
                    "oauth2Information",
                    FrontendOAuth2Information(
                        domain = clientRegistration.providerDetails.issuerUri.removeSuffix("/"),
                        expectedState = state,
                        authorizationUrl = authorizationUrl,
                    )
                )
            }
        }

        model.addAttribute(
            "smartbricksWebviewUrl",
            smartbricksService.getSmartbricksWebviewUrl(smartbricksProjectId),
        )

        return "smartbricks/webview"
    }

    @GetMapping("/download")
    fun downloadFile(
        @RequestParam signedUrl: String,
        @RequestParam filename: String
    ): ResponseEntity<StreamingResponseBody> {
        val streamingResponseBody = StreamingResponseBody { outputStream ->
            try {
                URL(signedUrl).openStream().use { inputStream ->
                    inputStream.copyTo(outputStream)
                }
            } catch (e: Exception) {
                log.error("Error while downloading $filename with signed url $signedUrl", e)
            }
        }

        val headers = HttpHeaders().apply {
            add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$filename\"")
            add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
        }

        return ResponseEntity.ok()
            .headers(headers)
            .body(streamingResponseBody)
    }

    private fun generateSecureRandomString(length: Int = 32): String {
        val random = SecureRandom()
        val bytes = ByteArray(length)
        random.nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }

    private fun generatePKCEChallenge(codeVerifier: String): String {
        val bytes = codeVerifier.toByteArray(Charsets.US_ASCII) // US_ASCII is specified in the RFC for PKCE
        val messageDigest = MessageDigest.getInstance("SHA-256")
        val digest = messageDigest.digest(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(digest)
    }
}

data class FrontendOAuth2Information(
    val domain: String,
    val expectedState: String,
    val authorizationUrl: String,
)
