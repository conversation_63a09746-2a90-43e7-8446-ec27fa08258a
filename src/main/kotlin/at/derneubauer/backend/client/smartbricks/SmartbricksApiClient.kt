package at.derneubauer.backend.client.smartbricks

import at.derneubauer.backend.client.smartbricks.model.request.CreateSmartbricksProjectRequest
import at.derneubauer.backend.client.smartbricks.model.response.CreateSmartbricksProjectResponse
import at.derneubauer.backend.client.smartbricks.model.response.SmartbricksDocumentItemResponse
import at.derneubauer.backend.client.smartbricks.model.response.SmartbricksImageItemResponse
import at.derneubauer.backend.client.smartbricks.model.response.SmartbricksSignedUrlMetadataResponse
import at.derneubauer.backend.config.SmartbricksProperties
import org.springframework.http.MediaType
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.web.reactive.function.client.ServletOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono

@Service
class SmartbricksApiClient(
    webClientBuilder: WebClient.Builder,
    private val smartbricksProperties: SmartbricksProperties,
    authorizedClientManager: OAuth2AuthorizedClientManager,
) {
    private val webClient: WebClient

    init {
        val oauth2Client = ServletOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager)
        oauth2Client.setDefaultClientRegistrationId(smartbricksProperties.clientRegistrationId)

        this.webClient = webClientBuilder
            .baseUrl(smartbricksProperties.apiBaseUrl)
            .apply(oauth2Client.oauth2Configuration())
            .build()
    }

    fun createProject(projectNumber: String): CreateSmartbricksProjectResponse = webClient.post()
        .uri("/projects")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(getCreateProjectRequest(projectNumber))
        .retrieve()
        .bodyToMono(CreateSmartbricksProjectResponse::class.java)
        .blockWithErrorHandling("create project with number $projectNumber")

    fun getDocumentsByProject(projectId: Long): List<SmartbricksDocumentItemResponse> = webClient.get()
        .uri("/documents/byProject/$projectId")
        .retrieve()
        .bodyToFlux(SmartbricksDocumentItemResponse::class.java)
        .collectList()
        .blockWithErrorHandling("get documents for project $projectId")

    fun getImagesByProject(projectId: Long): List<SmartbricksImageItemResponse> = webClient.get()
        .uri("/images/byProject/$projectId")
        .retrieve()
        .bodyToFlux(SmartbricksImageItemResponse::class.java)
        .collectList()
        .blockWithErrorHandling("get images for project $projectId")

    fun getSignedUrlMetadata(): SmartbricksSignedUrlMetadataResponse = webClient.get()
        .uri("/documents/signedUrl") // document signed url has to be used for both images and documents
        .retrieve()
        .bodyToMono(SmartbricksSignedUrlMetadataResponse::class.java)
        .blockWithErrorHandling("get signed url")

    private fun getCreateProjectRequest(projectNumber: String) = CreateSmartbricksProjectRequest(
        projectNumber = projectNumber,
        projectName = "Smartbricks Projekt für Arbeitsschein $projectNumber",
        managerUserId = smartbricksProperties.defaultApiParams.managerUserId,
        buildingObjectId = smartbricksProperties.defaultApiParams.buildingObjectId,
        clientId = smartbricksProperties.defaultApiParams.clientId,
        serviceCatalogId = smartbricksProperties.defaultApiParams.serviceCatalogId,
    )

    private fun <T> Mono<T>.blockWithErrorHandling(operation: String): T {
        return try {
            this.block() ?: throw SmartbricksApiException("Empty response from Smartbricks API for $operation")
        } catch (e: SmartbricksApiException) {
            throw e // rethrow unchanged
        } catch (e: Exception) {
            throw SmartbricksApiException("Failed to $operation: ${e.message}", e)
        }
    }
}
