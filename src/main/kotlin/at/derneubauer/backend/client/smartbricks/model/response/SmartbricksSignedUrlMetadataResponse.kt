package at.derneubauer.backend.client.smartbricks.model.response

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.web.util.UriComponentsBuilder
import java.net.URI
import java.time.OffsetDateTime

data class SmartbricksSignedUrlMetadataResponse(
    @field:JsonProperty("bucket")
    val baseUrl: String,
    val signedUrl: String,
    val expiresAt: OffsetDateTime,
)

fun SmartbricksSignedUrlMetadataResponse.generateUrlForDocument(document: SmartbricksDocumentItemResponse): String {
    val path = "${document.bucket}/${document.key}"
    return UriComponentsBuilder.fromUri(URI.create(baseUrl))
        .path(path)
        .query(signedUrl.removePrefix("?"))
        .toUriString()
}

fun SmartbricksSignedUrlMetadataResponse.generateUrlForImage(image: SmartbricksImageItemResponse): String {
    val path = "${image.bucket}/${image.key}"
    return UriComponentsBuilder.fromUri(URI.create(baseUrl))
        .path(path)
        .query(signedUrl.removePrefix("?"))
        .toUriString()
}
