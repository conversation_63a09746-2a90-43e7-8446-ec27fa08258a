package at.derneubauer.backend.web.images

import at.derneubauer.backend.client.SmsService
import at.derneubauer.backend.db.image.ImageDo
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.ImageType
import at.derneubauer.backend.service.ImageService
import at.derneubauer.backend.service.ResourcePlanService
import at.derneubauer.backend.service.SmartbricksService
import at.derneubauer.backend.service.UserService
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.core.io.FileSystemResource
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import org.springframework.web.servlet.view.RedirectView
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.io.Serializable
import java.time.ZonedDateTime
import java.util.Locale
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull


enum class PDFTemplates(val config: PDFTemplate) {
    ONEONE(PDFTemplate("1x1", ImageOrientation.PORTRAIT, "images/layouts/1-1", 1, 1200)),
    ONETWO(PDFTemplate("1x2", ImageOrientation.LANDSCAPE, "images/layouts/1-2", 2, 1200)),
    TWOTWO(PDFTemplate("2x2", ImageOrientation.PORTRAIT, "images/layouts/2-2", 4,  1200)),
    THREETHREE(PDFTemplate("3x3", ImageOrientation.PORTRAIT, "images/layouts/3-3", 9, 1200))
}

@Controller
class WebImagesController(
    private val imageService: ImageService,
    private val userResolver: LoggedInUserResolver,
    private val permissionService: PermissionService,
    private val resourcePlanService: ResourcePlanService,
    private val userService: UserService,
    private val messageSource: MessageSource,
    private val smsService: SmsService,
    private val smartbricksService: SmartbricksService,
) {

    @GetMapping("/admin/users/{userId}/images")
    fun imagesForUsers(@PathVariable userId: Long, model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.LIST_USER_IMAGES, user)

        model.addAttribute("id", userId)
        model.addAttribute("type", "users")
        model.addAttribute("images", imageService.readUserImagesWithRefId(userId).map { convertToImageViewModel(it) })
        model.addAttribute("requestImagesUrl", "/admin/users/${userId}/images/request")
        model.addAttribute("deleteImagesUrl", "/admin/users/${userId}/images/delete")
        model.addAttribute("downloadImagesUrl", "/admin/users/images/download")
        model.addAttribute("moveImagesUrl", "/admin/users/${userId}/images/move")

        return "images/grid"
    }

    @PostMapping("/admin/issues/{issueId}/images/request")
    fun requestIssueImages(@PathVariable issueId: Long, redirectAttributes: RedirectAttributes, locale: Locale): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.REQUEST_ISSUE_IMAGES_FROM_APP, user)

        try {
            smsService.requestIssueImages(issueId, user)
            redirectAttributes.addFlashAttribute("smsSuccess", messageSource.getMessage("sms.send.success", arrayOf(), locale))
        } catch(e: Exception) {
            redirectAttributes.addFlashAttribute("smsError", messageSource.getMessage("sms.send.error", arrayOf(), locale))
        }
        val redirectView = RedirectView("/admin/issues/${issueId}/images", true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @PostMapping("/admin/users/{userId}/images/request")
    fun requestUserImages(@PathVariable userId: Long, redirectAttributes: RedirectAttributes, locale: Locale): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.REQUEST_USER_IMAGES_FROM_APP, user)

        try {
            smsService.requestUserImages(userId, user)
            redirectAttributes.addFlashAttribute("smsSuccess", messageSource.getMessage("sms.send.success", arrayOf(), locale))
        } catch(e: Exception) {
            redirectAttributes.addFlashAttribute("smsError", messageSource.getMessage("sms.send.error", arrayOf(), locale))
        }

        val redirectView = RedirectView("/admin/users/${userId}/images?success", true)
        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @ResponseBody
    @GetMapping("/admin/users/images/{imageId}", produces = [MediaType.IMAGE_JPEG_VALUE])
    fun viewUserImage(@PathVariable imageId: Long): ByteArray {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_USER_IMAGE, user)

        return imageService.retrieveImage(imageId, ImageType.USER)
    }

    @ResponseBody
    @GetMapping("/admin/users/images/{imageId}/thumbnail", produces = [MediaType.IMAGE_JPEG_VALUE])
    fun viewUserThumbnail(@PathVariable imageId: Long): ByteArray {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_USER_IMAGE, user)

        return imageService.retrieveThumbnail(imageId, ImageType.USER)
    }

    @PostMapping("/admin/users/{userId}/images/delete")
    fun deleteUserImageWithId(@PathVariable userId: Long, model: Model, @Valid @ModelAttribute("deleteForm") form: DeleteForm, bindingResult: BindingResult): RedirectView {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DELETE_USER_IMAGE, user)

        for(imageId in form.images) {
            imageService.deleteUserImage(userId, imageId)
        }

        val redirectView = RedirectView("/admin/users/$userId/images", false)
        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @GetMapping("/admin/issues/{issueId}/images")
    fun imagesForIssue(@PathVariable issueId: Long, model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.LIST_ISSUE_IMAGES, user)

        model.addAttribute("id", issueId)
        model.addAttribute("type", "issues")
        model.addAttribute("images", imageService.readIssueImagesWithRefId(issueId).map { convertToImageViewModel(it) })
        model.addAttribute("smartbricksImages", smartbricksService.getSmartbricksImagesForIssue(issueId))
        model.addAttribute("requestImagesUrl", "/admin/issues/${issueId}/images/request")
        model.addAttribute("deleteImagesUrl", "/admin/issues/${issueId}/images/delete")
        model.addAttribute("downloadImagesUrl", "/admin/issues/images/download")
        model.addAttribute("moveImagesUrl", "/admin/issues/${issueId}/images/move")

        return "images/grid"
    }

    @ResponseBody
    @GetMapping("/admin/issues/images/{imageId}", produces = [MediaType.IMAGE_JPEG_VALUE])
    fun viewIssueImage(@PathVariable imageId: Long): ByteArray {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_IMAGE, user)

        return imageService.retrieveImage(imageId, ImageType.ISSUE)
    }

    @ResponseBody
    @GetMapping("/admin/issues/images/{imageId}/thumbnail", produces = [MediaType.IMAGE_JPEG_VALUE])
    fun viewIssueThumbnail(@PathVariable imageId: Long): ByteArray {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_IMAGE, user)

        return imageService.retrieveThumbnail(imageId, ImageType.ISSUE)
    }

    @PostMapping("/admin/issues/{issueId}/images/delete")
    fun deleteIssueImageWithId(@PathVariable issueId: Long, model: Model, @Valid @ModelAttribute("deleteForm") form: DeleteForm, bindingResult: BindingResult): RedirectView {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DELETE_ISSUE_IMAGE, user)

        for(imageId in form.images) {
            imageService.deleteIssueImage(issueId, imageId)
        }

        val redirectView = RedirectView("/admin/issues/$issueId/images", true)
        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @PostMapping("/admin/issues/{issueId}/images/move")
    fun moveIssueImages(@PathVariable issueId: Long, model: Model, @Valid @ModelAttribute("moveImagesForm") form: MoveImagesForm, bindingResult: BindingResult): RedirectView {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.MOVE_ISSUE_IMAGE, user)

        form.images.forEach { imageId ->
            imageService.moveIssueImageToOtherIssue(imageId, issueId, form.targetIssueId)
        }

        val redirectView = RedirectView("/admin/issues/$issueId/images", true)
        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @PostMapping("/admin/users/{userId}/images/move")
    fun moveUserImages(@PathVariable userId: Long, model: Model, @Valid @ModelAttribute("moveImagesForm") form: MoveImagesForm, bindingResult: BindingResult): RedirectView {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.MOVE_USER_IMAGE, user)

        form.images.forEach { imageId ->
            imageService.moveUserImageToIssue(imageId, userId, form.targetIssueId)
        }

        val redirectView = RedirectView("/admin/users/${userId}/images", true)
        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @ResponseBody
    @PostMapping("/admin/issues/images/download", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun downloadIssueImagePdf(locale: Locale, @Valid @ModelAttribute("downloadForm") form: DownloadForm, bindingResult: BindingResult, response: HttpServletResponse): DeleteOnCloseFileSystemResource {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DOWNLOAD_ISSUE_IMAGES, user)

        val now = ZonedDateTime.now()
        val template = PDFTemplates.valueOf(form.template)
        val issue = resourcePlanService.getIssueDetail(form.id)
        val filename = "Auftrag_" + issue.externalId + "_" + dateTimeForFilename(now) +  "_" + template.config.displayName + ".pdf"
        val title =   "Auftrag: ${issue.externalId}"

        response.setHeader("Content-Disposition", "attachment; filename="+filename)

        val pdfFile = imageService.createPDF(
            title,
            dateTimeForDocument(now),
            ImageType.ISSUE,
            template.config,
            form.images,
            locale
        )

        return DeleteOnCloseFileSystemResource(pdfFile)
    }

    @ResponseBody
    @PostMapping("/admin/users/images/download", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun downloadUserImagePdf(locale: Locale, @Valid @ModelAttribute("downloadForm") form: DownloadForm, bindingResult: BindingResult, response: HttpServletResponse): DeleteOnCloseFileSystemResource {
        if (bindingResult.hasErrors()) {
            val message = messageSource.getMessage("form.invalid", null,  LocaleContextHolder.getLocale())
            throw WebImagesControllerException(message)
        }

        val loggedInUser = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DOWNLOAD_USER_IMAGES, loggedInUser)

        val now = ZonedDateTime.now()
        val template = PDFTemplates.valueOf(form.template)
        val user = userService.getUserById(form.id)
        var usernameFileName = user.username.replace("[^A-Za-z0-9 ]".toRegex(), "")
        val filename = "Benutzer_${usernameFileName}_${dateTimeForFilename(now)}_${template.config.displayName}.pdf"
        val title = "Benutzer: ${usernameFileName}"

        response.setHeader("Content-Disposition", "attachment; filename=${filename}");

        val pdfFile = imageService.createPDF(
            title,
            dateTimeForDocument(now),
            ImageType.USER,
            template.config,
            form.images,
            locale
        )

        return DeleteOnCloseFileSystemResource(pdfFile)
    }

    private fun convertToImageViewModel(image: ImageDo): ImageViewModel {
        if (image.refType == ImageType.ISSUE.toString()) {
            return ImageViewModel(
                image.id,
                "/admin/issues/images/${image.id}",
                "/admin/issues/images/${image.id}/thumbnail",
                "/admin/issues/${image.refId}/images/${image.id}/delete"
            )
        } else {
            return ImageViewModel(
                image.id,
                "/admin/users/images/${image.id}",
                "/admin/users/images/${image.id}/thumbnail",
                "/admin/users/${image.refId}/images/${image.id}/delete"
            )
        }
    }

    private fun dateTimeForFilename(date: ZonedDateTime): String {
        return NeubauerDateFormatter.fileNameDateFormatter.format(date)
    }

    private fun dateTimeForDocument(date: ZonedDateTime): String {
        return NeubauerDateFormatter.dateTimeFormatter.format(date)
    }
}

data class DownloadForm (
    @get:NotNull(message="{errors.id.notEmpty}")
    var id: Long = 0,

    @get:NotEmpty(message="{errors.template.notEmpty}")
    var template: String = "ONEONE",

    @get:NotEmpty(message="{errors.image.notEmpty}")
    var images: Array<Long> = arrayOf()
) : Serializable

data class DeleteForm (
    @get:NotEmpty(message="{errors.image.notEmpty}")
    var images: Array<Long> = arrayOf()
) : Serializable

data class MoveImagesForm(
    @get:NotEmpty(message="{errors.image.notEmpty}")
    var images: Array<Long> = arrayOf(),

    @get:NotNull(message = "{errors.issue.notEmpty}")
    var targetIssueId: Long = 0
) : Serializable

data class ImageViewModel(
    val id: Long,
    val imageUrl: String,
    val thumbnailUrl: String,
    val deleteUrl: String
)

enum class ImageOrientation() {
    PORTRAIT,
    LANDSCAPE
}

data class PDFTemplate(
    val displayName: String,
    val imageOrientation: ImageOrientation,
    val layout: String,
    val chunkSize: Int,
    val imageSize: Int
)

open class WebImagesControllerException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class DeleteOnCloseFileSystemResource(file: File): FileSystemResource(file) {

    override fun getInputStream(): InputStream {
        return DeleteOnCloseFileInputStream(super.getFile())
    }
}

class DeleteOnCloseFileInputStream(private val file: File): FileInputStream(file) {

    override fun close() {
        super.close()
        file.delete()
    }
}
