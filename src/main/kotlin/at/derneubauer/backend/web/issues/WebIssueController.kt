package at.derneubauer.backend.web.issues

import at.derneubauer.backend.db.issue.RecurringIssueDo
import at.derneubauer.backend.rest.error.NeubauerRestExceptionHandler
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.AssignmentDateAndTechnicianDto
import at.derneubauer.backend.service.EventType
import at.derneubauer.backend.service.IssueDetailDto
import at.derneubauer.backend.service.IssueNotFoundResourcePlanServiceException
import at.derneubauer.backend.service.IssueService
import at.derneubauer.backend.service.NeubauerIssueStatus
import at.derneubauer.backend.service.RecurringIssueService
import at.derneubauer.backend.service.ResourcePlanService
import at.derneubauer.backend.service.UserService
import at.derneubauer.backend.util.ColorMappings
import at.derneubauer.backend.util.DetailUrlType
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.util.UrlMappings
import at.derneubauer.backend.web.error.NeubauerException
import at.derneubauer.backend.web.scheduler.AssignmentDateAndTechnicianViewModel
import at.derneubauer.backend.web.scheduler.InvalidDateFormatException
import at.derneubauer.backend.web.scheduler.InvalidDateOrTimeFormatException
import at.derneubauer.backend.web.scheduler.InvalidTimeFormatException
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import org.apache.commons.codec.digest.DigestUtils
import org.hibernate.validator.constraints.Length
import org.hibernate.validator.constraints.Range
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.validation.FieldError
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import org.springframework.web.servlet.view.RedirectView
import java.io.Serializable
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeParseException
import java.util.*


@Controller
class WebIssueController(
    private val messageSource: MessageSource,
    private val userResolver: LoggedInUserResolver,
    private val permissionService: PermissionService,
    private val colorMappings: ColorMappings,
    private val resourcePlanService: ResourcePlanService,
    private val urlMappings: UrlMappings,
    private val issueService: IssueService,
    private val userService: UserService,
    private val recurringIssueService: RecurringIssueService,
) {

    companion object {
        private val springBindingPrefix = "org.springframework.validation.BindingResult"
        private val issueDetailNoteBinding = "issueDetailFormBindingResult"
        private val assignmentDetailBinding = "assignmentDetailFormBindingResult"
        private val recurringIssueBinding = "recurringIssueFormBindingResult"
        private val subIssueAssignmentBinding = "subIssueAssignmentFormBindingResult"

        private val log = LoggerFactory.getLogger(NeubauerRestExceptionHandler::class.java)
    }

    @GetMapping("/admin/issues")
    fun issueList(model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_LIST, user)

        model.addAttribute("issueStatusList", NeubauerIssueStatus.values())

        return "issue/issue-list"
    }

    @GetMapping("/issue/{issueId}")
    fun issueAssignmentDetail(@PathVariable issueId: Long,
                              @RequestParam("assignment", required = false) assignmentId: Long?,
                              @RequestParam("mode", required = false) mode: DetailAssignmentPreselectMode?,
                              @RequestParam("shouldValidate", required = false) shouldValidate: Boolean = true,
                              assignmentDetail: AssignmentDetail,
                              issueDetailNote: IssueDetailNote,
                              recurringIssue: RecurringIssue,
                              subIssueAssignment: SubIssueAssignment,
                              model: Model,
                              bindingResult: BindingResult,
                              redirectAttributes: RedirectAttributes,
                              locale: Locale): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_DETAIL, user)

        var assignmentDetailViewModel: AssignmentDetailViewModel? = null
        val modeInternal = mode ?: DetailAssignmentPreselectMode.DETAIL
        if (assignmentId != null) {
            val assignmentDateAndTechnician = resourcePlanService.getIssueAssignmentDetail(assignmentId)
            if (assignmentDateAndTechnician != null) {
                assignmentDetailViewModel = AssignmentDetailViewModel(assignmentDateAndTechnician)
            }
        }
        val issueDetails = resourcePlanService.getIssueDetail(issueId)

        val issueDetailFormBinding = model.asMap().get(issueDetailNoteBinding)
        val assignmentDetailFormBinding = model.asMap().get(assignmentDetailBinding)
        val subIssueAssignmentBinding = model.asMap().get(subIssueAssignmentBinding)
        if (subIssueAssignmentBinding != null) {
            model.addAttribute("${springBindingPrefix}.subIssueAssignment", subIssueAssignmentBinding)
        }

        val recurringIssueBinding = model.asMap().get(recurringIssueBinding)
        if (recurringIssueBinding != null && shouldValidate) {
            model.addAttribute("${springBindingPrefix}.recurringIssue", recurringIssueBinding)
        } else {
            recurringIssue.frequency = 0
        }

        val issues = recurringIssueService.findRecurringIssuesForIssueId(issueId)
        if (issues.isNotEmpty()) {
            val issueViewModels = issues.map {
                val subIssueAssignmentId = it.subIssueId?.let { subIssueId ->
                    resourcePlanService.getAssignmentForIssue(subIssueId)?.id
                }
                RecurringIssueViewModel(userService, issueService, it, urlMappings, subIssueAssignmentId)
            }
            model.addAttribute("recurringIssues", issueViewModels)
        }
        val mainIssue = recurringIssueService.findMainIssueForRecurring(issueId)
        val mainIssueId = mainIssue?.issueId ?: issueId
        model.addAttribute("recurringMainIssue",
                RecurringMainIssueViewModel(
                        resourcePlanService.getIssueDetail(mainIssueId),
                        urlMappings,
                        assignmentId
                )
        )

        val isInitialLoadOfDetailForm = issueDetailNote.initialNoteHash.isBlank()
        if (issueDetailFormBinding != null) {
            model.addAttribute("${springBindingPrefix}.issueDetailNote", issueDetailFormBinding)
        } else if (isInitialLoadOfDetailForm) {
            issueDetailNote.initialNoteHash = md5Hash(issueDetails.note ?: "")
            issueDetailNote.note = issueDetails.note ?: ""
        }

        val isInitialLoadOfAssignmentForm = assignmentDetail.endDate.isBlank()
        if (assignmentDetailFormBinding != null) {
            model.addAttribute("${springBindingPrefix}.assignmentDetail", assignmentDetailFormBinding)
        } else if (isInitialLoadOfAssignmentForm) {
            assignmentDetail.startDate = assignmentDetailViewModel?.getStartDateFormatted() ?: ""
            assignmentDetail.endDate = assignmentDetailViewModel?.getEndDateFormatted() ?: ""
            assignmentDetail.startTime = assignmentDetailViewModel?.getStartTimeFormatted() ?: ""
            assignmentDetail.endTime = assignmentDetailViewModel?.getEndTimeFormatted() ?: ""
        }

        if (issueService.isIssueInStateThatCanBeClosed(issueDetails.status)) {
            model.addAttribute("closeIssueUrl", "/issue/${issueId}/close")
            model.addAttribute("displayCloseIssueButton", true)
        }

        if (issueService.isIssueInStateThatCanBeReopened(issueDetails.status)) {
            model.addAttribute("reopenIssueUrl", "/issue/${issueId}/reopen")
            model.addAttribute("displayReopenIssueButton", true)
        }

        val isRecurringIssue = recurringIssueService.findIssueIdsInRecurringIssues().contains(issueId)

        model.addAttribute("gradientStartColor", colorMappings.forEventType(EventType.ISSUE, issueDetails.status.name).startColor)
        model.addAttribute("gradientEndColor", colorMappings.forEventType(EventType.ISSUE, issueDetails.status.name).endColor)

        model.addAttribute("issue",
                IssueDetailViewModel(
                        issueDetails,
                        locale,
                        messageSource,
                        urlMappings,
                        modeInternal,
                        assignmentDetailViewModel?.getId(),
                        isRecurringIssue
                )
        )
        model.addAttribute("assignment", assignmentDetailViewModel)
        model.addAttribute("frequencies", RecurringIssueFrequency.values())
        model.addAttribute("subIssues", issueService.findAssignableSubIssues())
        model.addAttribute("isRecurringIssue", isRecurringIssue)
        model.addAttribute("isAssignedSubIssue", recurringIssueService.findAssignedRecurringIssues().map { it.subIssueId }.contains(issueId))

        model.addAttribute("scanUploadUrl", urlMappings.scanUploadUrl(issueId))
        model.addAttribute("scanDeleteBaseUrl", urlMappings.scanDeleteBaseUrl())

        model.addAttribute("unassignSubIssueUrl", "/issue/${issueId}/unassign")
        model.addAttribute("deleteAssignmentUrl", "/issue/${issueId}/assignment")

        return "issue/detail"
    }

    @PostMapping("/issue/{issueId}/detail")
    fun issueDetail(@PathVariable issueId: Long,
                    @RequestParam("assignment", required = false) assignmentId: Long?,
                    @Valid @ModelAttribute("issueDetailNote") issueDetailNote: IssueDetailNote,
                    bindingResult: BindingResult,
                    redirectAttributes: RedirectAttributes,
                    locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.EDIT_ISSUE_DETAIL_NOTE, user)

        val redirectUrl = urlMappings.detailUrlForEventType(EventType.ISSUE, issueId, DetailUrlType.PRIMARY, assignmentId)
        redirectAttributes.addFlashAttribute(WebIssueController.issueDetailNoteBinding, bindingResult)

        if (bindingResult.hasErrors()) {
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        return try {
            val savedNote = issueService.findIssue(issueId).note
            val savedNoteHash = md5Hash(savedNote ?: "")

            if (issueDetailNote.initialNoteHash != savedNoteHash) {
                redirectBackWithError("issue.detail.note.error", locale, redirectAttributes, redirectUrl)
            } else {
                issueService.editNote(issueId, issueDetailNote.note)
                issueDetailNote.initialNoteHash = md5Hash(issueDetailNote.note)
                redirectBackWithSuccess("issue.detail.note.success", locale, redirectAttributes, redirectUrl)
            }

        } catch (e: NeubauerException) {
            redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @PostMapping("/issue/{issueId}/assignment")
    fun absenceDetailSave(@PathVariable issueId: Long,
                          @RequestParam("assignment") assignmentId: Long,
                          @Valid @ModelAttribute("assignmentDetail") assignmentDetail: AssignmentDetail,
                          bindingResult: BindingResult,
                          redirectAttributes: RedirectAttributes,
                          locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.EDIT_ISSUE_TECHNICIAN_ASSIGNMENT, user)

        val redirectUrl = urlMappings.detailUrlForEventType(EventType.ISSUE, issueId, DetailUrlType.SECONDARY, assignmentId)
        redirectAttributes.addFlashAttribute(WebIssueController.assignmentDetailBinding, bindingResult)

        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", bindingResult.getFieldError())
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        try {
            val startDate = combineDateAndTime(
                    parseDate(assignmentDetail.startDate, "startDate"),
                    parseTime(assignmentDetail.startTime, "startTime")
            )

            val endDate = combineDateAndTime(
                    parseDate(assignmentDetail.endDate, "endDate"),
                    parseTime(assignmentDetail.endTime, "endTime")
            )

            resourcePlanService.updateIssueTechniciansAssignmentDate(
                    assignmentId,
                    startDate,
                    endDate
            )

            return redirectBackWithSuccess("issue.assignment.edit.success", locale, redirectAttributes, redirectUrl)
        } catch (e: InvalidDateOrTimeFormatException) {
            bindingResult.addError(FieldError("assignmentDetail", e.fieldName, messageSource.getMessage(e.localizationKey, arrayOf(), locale)))
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @PostMapping("/issue/{issueId}/recurringIssue")
    fun generateRecurringIssues(@PathVariable issueId: Long,
                                @RequestParam("assignment") assignmentId: Long?,
                                @Valid @ModelAttribute("recurringIssue") recurringIssue: RecurringIssue,
                                bindingResult: BindingResult,
                                redirectAttributes: RedirectAttributes,
                                locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.CREATE_RECURRING_ISSUE_PLACEHOLDERS, user)

        val redirectUrl = urlMappings.detailUrlForEventType(EventType.ISSUE, issueId, DetailUrlType.TERTIARY, assignmentId)
        redirectAttributes.addFlashAttribute(recurringIssueBinding, bindingResult)

        val repetition = recurringIssue.repetitions ?: 0
        if (bindingResult.hasErrors() || repetition <= 0 || repetition > 99) {
            redirectAttributes.addFlashAttribute("error", bindingResult.getFieldError())
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        try {
            val frequency = RecurringIssueFrequency.values()[recurringIssue.frequency]
            recurringIssueService.generateRecurringIssues(issueId, repetition, frequency.interval, frequency.unit)
            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("recurringissues.create.success", arrayOf(), locale))
            return redirectBackWithSuccess("recurringissues.create.success", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @PostMapping("issue/{issueId}/subIssueAssignment")
    fun saveSubIssueAssignment(@PathVariable issueId: Long,
                               @RequestParam("assignment") assignmentId: Long?,
                               @Valid @ModelAttribute("subIssueAssignment") subIssueAssignment: SubIssueAssignment,
                               bindingResult: BindingResult,
                               redirectAttributes: RedirectAttributes,
                               locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.CREATE_RECURRING_ISSUE_PLACEHOLDERS, user)

        val redirectUrl = urlMappings.detailUrlForEventType(EventType.ISSUE, issueId, DetailUrlType.TERTIARY, assignmentId, shouldValidate = false)
        redirectAttributes.addFlashAttribute(recurringIssueBinding, bindingResult)

        // validate assignments
        val subIssueIds = subIssueAssignment.subIssues.values.filterNotNull()
        val hasDuplicates = subIssueIds.groupingBy { it }.eachCount().any { it.value > 1 }
        if (hasDuplicates) {
            return redirectBackWithError("recurringissues.subissue.duplicate", locale, redirectAttributes, redirectUrl)
        }

        return try {
            subIssueAssignment.subIssues.forEach { recurringIssueId, subissueId ->
                if (subissueId != null) {
                    recurringIssueService.assignSubIssue(user.id, recurringIssueId, subissueId)
                }
            }

            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("recurringissues.subissue.success", arrayOf(), locale))
            redirectBackWithSuccess("recurringissues.subissue.success", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    private fun parseTime(time: String, fieldName: String): LocalTime {
        try {
            return LocalTime.parse(time, NeubauerDateFormatter.timeFormatter)
        } catch (e: DateTimeParseException) {
            throw InvalidTimeFormatException(fieldName, "Unable to parse date.", e)
        }
    }

    private fun parseDate(date: String, fieldName: String): LocalDate {
        try {
            return LocalDate.parse(date, NeubauerDateFormatter.dateFormatter)
        } catch (e: DateTimeParseException) {
            throw InvalidDateFormatException(fieldName, "Unable to parse date.", e)
        }
    }

    private fun combineDateAndTime(date: LocalDate, time: LocalTime): ZonedDateTime =
            date.atTime(time).atZone(NeubauerDateFormatter.timeZone)

    @PostMapping("/issue/{issueId}/close")
    fun closeIssue(@PathVariable issueId: Long,
                   @RequestParam("assignment") assignmentId: Long?,
                   redirectAttributes: RedirectAttributes, locale: Locale): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.CLOSE_ISSUE, user)
        redirectAttributes.addAttribute("assignment", assignmentId)
        try {
            issueService.closeIssue(user, issueId)
            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("status.change.success", arrayOf(), locale))
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("status.change.error", arrayOf(), locale))
        }

        val redirectView = RedirectView("/issue/${issueId}", true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    @PostMapping("/issue/{issueId}/unassign")
    fun unassignSubIssue(
        @PathVariable issueId: Long,
        @RequestParam("assignment") assignmentId: Long,
        redirectAttributes: RedirectAttributes,
        locale: Locale
    ): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.EDIT_ASSISTANT_TECHNICIAN_ASSIGNMENT, user)

        redirectAttributes.addAttribute("assignment", assignmentId)

        try {
            recurringIssueService.unassignSubIssue(issueId)
            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("issue.recurring.unassign.success", emptyArray(), locale))
        } catch (e: Exception) {
            log.error("Exception occurred when trying to unassign subissue", e)
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("issue.recurring.unassign.error", emptyArray(), locale))
        }

        return RedirectView("/issue/${issueId}", true).apply {
            setHttp10Compatible(false)
        }
    }

    @ResponseBody
    @DeleteMapping("/issue/{issueId}/assignment")
    fun deleteAssignment(
        @PathVariable issueId: Long,
        @RequestParam("assignment") assignmentId: Long,
        redirectAttributes: RedirectAttributes, locale: Locale
    ): ResponseEntity<Unit> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DELETE_ISSUE_TECHNICIAN_ASSIGNMENT, user)

        try {
            resourcePlanService.deleteIssueTechnicianAssignment(
                assignmentId,
                user.id
            )
        } catch (e: Exception) {
            log.error("Exception occurred when trying to delete assignment", e)
            return ResponseEntity.internalServerError().build()
        }

        return ResponseEntity.ok().build()
    }

    @PostMapping("/issue/{issueId}/reopen")
    fun reopenIssue(@PathVariable issueId: Long,
                    @RequestParam("assignment") assignmentId: Long?,
                    redirectAttributes: RedirectAttributes, locale: Locale): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE, user)
        redirectAttributes.addAttribute("assignment", assignmentId)
        try {
            issueService.reopenIssue(user, issueId)
            redirectAttributes.addFlashAttribute("success", messageSource.getMessage("status.change.success", arrayOf(), locale))
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("error", messageSource.getMessage("status.change.error", arrayOf(), locale))
        }

        val redirectView = RedirectView("/issue/${issueId}", true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }

    private fun redirectBackWithSuccess(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView =
            redirectWithMessage("success", localizationKey, locale, redirectAttributes, redirectUrl)

    private fun redirectBackWithError(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView =
            redirectWithMessage("error", localizationKey, locale, redirectAttributes, redirectUrl)

    private fun redirectWithMessage(attributeName: String, localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView {
        redirectAttributes.addFlashAttribute(attributeName, messageSource.getMessage(localizationKey, arrayOf(), locale))

        val redirectView = RedirectView(redirectUrl, true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)

        return redirectView
    }

    private fun md5Hash(toHash: String): String {
        return DigestUtils.md5Hex(toHash)
    }

}

class IssueDetailViewModel(private val issue: IssueDetailDto,
                           private val locale: Locale,
                           private val messageSource: MessageSource,
                           private val urlMappings: UrlMappings,
                           private val mode: DetailAssignmentPreselectMode,
                           private val assignmentId: Long?,
                           private val isRecurringIssue: Boolean) {

    fun getExternalId(): String = issue.externalId

    fun getContactPerson(): String = issue.contactPerson ?: "-"

    fun getAddress(): String = issue.address ?: "-"

    fun getSuggestedDate(): String = issue.suggestedDate ?: "-"

    fun getDescription(): String = issue.description ?: "-"

    fun getNote(): String = issue.note ?: "-"

    fun getStatus(): String = messageSource.getMessage(
            issue.status.localizationKey,
            arrayOf(),
            locale
    )

    fun getAssignmentId() = assignmentId

    fun getAssignments(): List<AssignmentDateAndTechnicianViewModel> = issue.assignedDatesAndTechnicians.map {
        AssignmentDateAndTechnicianViewModel(it)
    }

    fun getShowAssignmentTab(): Boolean = assignmentId != null

    fun getShowRecurringIssueTab(): Boolean = assignmentId != null || isRecurringIssue

    fun getPreselectAssignmentTab(): Boolean = mode == DetailAssignmentPreselectMode.ASSIGNMENT

    fun getPreselectDetailTab(): Boolean = mode == DetailAssignmentPreselectMode.DETAIL

    fun getPreselectRecurringIssueTab(): Boolean = mode == DetailAssignmentPreselectMode.RECURRING_ISSUE

    fun getAssignmentDetailFormSubmitUrl(): String? =
            urlMappings.detailUrlForEventType(EventType.ISSUE, issue.id, DetailUrlType.SECONDARY, assignmentId, "/assignment")

    fun getIssueDetailFormSubmitUrl(): String? =
            urlMappings.detailUrlForEventType(EventType.ISSUE, issue.id, DetailUrlType.PRIMARY, assignmentId, "/detail")

    fun getAssignSubIssueFormSubmitUrl(): String? = urlMappings.assignSubissueUrl(issue.id, assignmentId)

    fun getRecurringIssueSubmitUrl(): String? = urlMappings.recurringIssueUrl(issue.id, assignmentId)

    fun getSmartbricksProjectId(): Long? = issue.smartbricksProjectId
}

data class RecurringIssue(

        @get:Range(min = 1, max = 9, message = "{errors.recurringissue.frequency.range}")
        var frequency: Int = 0,

        @get:NotNull(message = "{errors.recurringissue.repetitions.notBlank}")
        @get:Range(min = 1, max = 99, message = "{errors.recurringissue.repetitions.range}")
        var repetitions: Int?

) : Serializable

class RecurringIssueViewModel(
        private val userService: UserService,
        private val issueService: IssueService,
        private val issue: RecurringIssueDo,
        private val urlMappings: UrlMappings,
        private val assignmentId: Long?
) {
    fun getId(): Long = issue.id

    fun getDate(): String = NeubauerDateFormatter.dateFormatter.format(issue.placeholderDateFrom)

    fun getIssueNumber(): String? {
        try {
            issue.subIssueId?.let {
                return issueService.findIssue(it).externalId
            }
            return null
        } catch (e: IssueNotFoundResourcePlanServiceException) {
            return null
        }
    }

    fun getTechnicianName(): String {
        val user = userService.getUserById(issue.userId)
        return "${user.lastName} ${user.firstName}"
    }

    fun getIssueUrl(): String? = urlMappings.issueUrl(issue.subIssueId, assignmentId)

    fun getDateUrl(): String = urlMappings.dateUrl(issue.placeholderDateFrom)
}

class RecurringMainIssueViewModel(
        private val issue: IssueDetailDto,
        private val urlMappings: UrlMappings,
        private val assignmentId: Long?
) {
    fun getDate(): String = NeubauerDateFormatter.dateFormatter.format(issue.assignedDatesAndTechnicians.first().startDate)

    fun getIssueNumber(): String = issue.externalId

    fun getTechnicianName(): String = "${issue.assignedDatesAndTechnicians.first().lastName} ${issue.assignedDatesAndTechnicians.first().firstName}"

    fun getIssueUrl(): String? = urlMappings.issueUrl(issue.id, assignmentId)

    fun getDateUrl():String = urlMappings.dateUrl(issue.assignedDatesAndTechnicians.first().startDate)
}

data class IssueDetailNote(

        @get:Length(max = 8192, message = "{errors.input.tooLong}")
        var note: String = "",
        var initialNoteHash: String = ""

) : Serializable

data class AssignmentDetail(

        var startDate: String = "",

        var endDate: String = "",

        var startTime: String = "",

        var endTime: String = ""

) : Serializable

data class SubIssueAssignment(
        var subIssues: Map<Long, Long?> = mutableMapOf()
) : Serializable

class AssignmentDetailViewModel(private val assignment: AssignmentDateAndTechnicianDto) {

    fun getId(): Long = assignment.id

    fun getStartDateFormatted(): String = NeubauerDateFormatter.dateFormatter.format(assignment.startDate)

    fun getEndDateFormatted(): String = NeubauerDateFormatter.dateFormatter.format(assignment.endDate)

    fun getStartTimeFormatted(): String = NeubauerDateFormatter.timeFormatter.format(assignment.startDate)

    fun getEndTimeFormatted(): String = NeubauerDateFormatter.timeFormatter.format(assignment.endDate)

    fun getTechnicianName(): String = "${assignment.lastName} ${assignment.firstName}"
}

enum class DetailAssignmentPreselectMode {
    ASSIGNMENT,
    DETAIL,
    RECURRING_ISSUE
}

enum class RecurringIssueFrequency(val localizationKey: String, val interval: Long, val unit: RecurringIssueTimeUnit) {
    NONE("recurringissue.none", 0, RecurringIssueTimeUnit.DAYS),
    WEEKLY("recurringissue.weekly", 7, RecurringIssueTimeUnit.DAYS),
    BI_WEEKLY("recurringissue.biweekly", 14, RecurringIssueTimeUnit.DAYS),
    MONTHLY("recurringissue.monthly", 1, RecurringIssueTimeUnit.MONTHS),
    TWO_MONTHLY("recurringissue.twomonthly", 2, RecurringIssueTimeUnit.MONTHS),
    THREE_MONTHLY("recurringissue.threemonthly", 3, RecurringIssueTimeUnit.MONTHS),
    FOUR_MONTHLY("recurringissue.fourmonthly", 4, RecurringIssueTimeUnit.MONTHS),
    FIVE_MONTHLY("recurringissue.fivemonthly", 5, RecurringIssueTimeUnit.MONTHS),
    HALF_YEARLY("recurringissue.halfyearly", 6, RecurringIssueTimeUnit.MONTHS),
    YEARLY("recurringissue.yearly", 12, RecurringIssueTimeUnit.MONTHS);

    fun getOrdinal() = ordinal
}

enum class RecurringIssueTimeUnit {
    DAYS,
    MONTHS
}
