package at.derneubauer.backend.web.documents.model

import at.derneubauer.backend.offa.unmarshal.OffaDocumentType

data class IssueDocuments(
    val offaDocuments: List<OffaDocumentListItem>,
    val smartbricksDocuments: List<SmartbricksDocumentListItem>,
)

data class OffaDocumentListItem(
    val id: Long,
    val documentType: OffaDocumentType,
    val filename: String,
    val updatedAt: String,
)

data class SmartbricksDocumentListItem(
    val name: String,
    val signedUrl: String,
)
