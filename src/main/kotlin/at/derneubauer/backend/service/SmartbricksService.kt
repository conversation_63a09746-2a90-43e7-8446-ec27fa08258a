package at.derneubauer.backend.service

import at.derneubauer.backend.client.smartbricks.SmartbricksApiClient
import at.derneubauer.backend.client.smartbricks.SmartbricksApiException
import at.derneubauer.backend.client.smartbricks.model.response.SmartbricksSignedUrlMetadataResponse
import at.derneubauer.backend.client.smartbricks.model.response.generateUrlForDocument
import at.derneubauer.backend.client.smartbricks.model.response.generateUrlForImage
import at.derneubauer.backend.config.SmartbricksProperties
import at.derneubauer.backend.store.SmartbricksIssueStore
import at.derneubauer.backend.web.documents.model.SmartbricksDocumentListItem
import at.derneubauer.backend.web.error.NeubauerException
import at.derneubauer.backend.web.images.model.SmartbricksImageListItem
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class SmartbricksService(
    private val smartbricksIssueStore: SmartbricksIssueStore,
    private val smartbricksApiClient: SmartbricksApiClient,
    private val authorizedClientService: OAuth2AuthorizedClientService,
    private val smartbricksProperties: SmartbricksProperties,
) {
    companion object {
        private val log = LoggerFactory.getLogger(this::class.java)

        private var cachedSignedUrlMetadata: SmartbricksSignedUrlMetadataResponse? = null
    }

    fun hasUserAccessTokens(): Boolean {
        val authentication = SecurityContextHolder.getContext().authentication

        if (authentication == null || !authentication.isAuthenticated) {
            return false
        }

        val authorizedClient: OAuth2AuthorizedClient? = authorizedClientService.loadAuthorizedClient(
            smartbricksProperties.clientRegistrationId,
            authentication.name,
        )

        return authorizedClient != null
    }

    fun createAndAssociateSmartbricksProject(issueId: Long, worksheetNumber: String) = try {
        val smartbricksProjectId = smartbricksApiClient.createProject(projectNumber = worksheetNumber).projectId
        smartbricksIssueStore.associateIssueWithSmartbricksProject(
            issueId = issueId,
            smartbricksProjectId = smartbricksProjectId,
        )
    } catch (ex: SmartbricksApiException) {
        log.error("Error creating project for issue ($worksheetNumber) in smartbricks", ex)
    } catch (ex: NeubauerException) {
        log.error("Error associating issue ($issueId) with smartbricks project", ex)
    }

    fun getSmartbricksDocumentsForIssue(issueId: Long): List<SmartbricksDocumentListItem> {
        if (!hasUserAccessTokens()) {
            return emptyList()
        }

        return try {
            val smartbricksProjectId =
                smartbricksIssueStore.getSmartbricksProjectIdForIssue(issueId) ?: return emptyList()

            val documents = smartbricksApiClient.getDocumentsByProject(smartbricksProjectId)

            val signedUrlMetadata = getValidSignedUrlMetadata()
            documents.map {
                SmartbricksDocumentListItem(
                    name = it.key,
                    signedUrl = signedUrlMetadata.generateUrlForDocument(it),
                )
            }
        } catch (ex: SmartbricksApiException) {
            log.error("Error retrieving documents for issue ($issueId) from smartbricks", ex)
            emptyList()
        } catch (ex: NeubauerException) {
            log.error("Error getting smartbricks id for issue ($issueId) from database", ex)
            emptyList()
        }
    }

    fun getSmartbricksImagesForIssue(issueId: Long): List<SmartbricksImageListItem> {
        if (!hasUserAccessTokens()) {
            return emptyList()
        }

        return try {
            val smartbricksProjectId =
                smartbricksIssueStore.getSmartbricksProjectIdForIssue(issueId) ?: return emptyList()

            val images = smartbricksApiClient.getImagesByProject(smartbricksProjectId)

            val signedUrlMetadata = getValidSignedUrlMetadata()
            images.map {
                SmartbricksImageListItem(
                    name = it.key,
                    signedUrl = signedUrlMetadata.generateUrlForImage(it),
                )
            }
        } catch (ex: SmartbricksApiException) {
            log.error("Error retrieving images for issue ($issueId) from smartbricks", ex)
            emptyList()
        } catch (ex: NeubauerException) {
            log.error("Error getting smartbricks id for issue ($issueId) from database", ex)
            emptyList()
        }
    }

    fun getSmartbricksWebviewUrl(projectId: Long) =
        "${smartbricksProperties.baseUrl}projects/${projectId}/constructionNotes"

    private fun getValidSignedUrlMetadata(): SmartbricksSignedUrlMetadataResponse = synchronized(this) {
        cachedSignedUrlMetadata?.takeIf {
            OffsetDateTime.now().plusHours(1)
                .isBefore(it.expiresAt) // only use cached url if it's valid for at least one more hour
        } ?: smartbricksApiClient.getSignedUrlMetadata().also {
            cachedSignedUrlMetadata = it
        }
    }
}
