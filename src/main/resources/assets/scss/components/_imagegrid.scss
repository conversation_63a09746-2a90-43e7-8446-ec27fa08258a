@use "variables";
.thumbnail {
    margin-bottom: 20px;
    position: relative;
    width: 100%;
    background-size: cover;
    background-position: center;
    border-radius: 10px;
    overflow: hidden;
    &::after {
      content: "";
      display: block;
      padding-bottom: 100%;
    }
    .delete {
        position: absolute;
        top: 10px;
        left: 10px;
        button {
            cursor: pointer;
            padding: 6px;
            border: 0px;
            background: white;
            border-radius: 3px;
            svg {
                fill: variables.$primary;
                width: 12px;
                height: 12px;
                display: block;
            }
            &:hover {
                background: variables.$primary;
                svg {
                    fill: white;
                }
            }
        }
    }
}


.template-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 7px 20px;
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.2);
    display: inline-block;
    font-weight: normal;
    font-size: 14px;
    color: #505a64;
    width: 100%;
    margin-right: 10px;
}

.image-content {
    min-height: 365px;
    background-color: #FAFAFA;
    padding-top: 50px;
    padding-bottom: 20px;
}

input.checkbox {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input.checkbox::after {
    background: transparent;
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

input.checkbox:checked::after {
    background: #266BCC;
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

input.checkbox::before {
    content: " ";
    background: white;
    width: 20px;
    height: 20px;
    display: block;
    border-radius: 3px;
    border: 1px solid #266BCC;
    position: absolute;
    z-index: 1;
    top: 10px;
    left: 10px;
}

input.checkbox:checked::before {
    background: #266BCC;
    background-image: url(../images/icon/check.png);
    background-size: contain;
}

.image-grid-users {
    height: 576px;
    padding-top: 152px;
    overflow-x: hidden;
    overflow-y: scroll;

    .image-content {
        min-height: 575px;
        padding-top: 75px;
    }
}

.image-grid-issues {
    height: 312px;
    padding-top: 175px;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-bottom: 75px;

    .image-content {
        min-height: 311px;
    }
}

.image-grid-issues ~ .position-fixed-iframe {
    padding-left: 50px;
    padding-right: 50px;
}

.fixed-navbar  {
    top: 0;
    left: 0;
    right: 0;
    border-bottom: 1px solid #dddddd;
    position: fixed;
    z-index: 2;
    background: white;
    padding-top: 36px;
}

.image-grid-users .fixed-navbar {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.image-grid-users .image-content {
    padding-left: 30px !important;
    padding-right: 30px !important;
}

.smartbricks-image {
  margin-bottom: 20px;
  position: relative;
  width: 100%;
  height: 178px;
  background-size: cover;
  background-position: center;
  border-radius: 10px;
  overflow: hidden;

  .smartbricks-hint {
    position: absolute;
    bottom: 10px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    background: variables.$primary;
    width: 60%;
    padding: 5px 10px;
    text-align: center;
    border-bottom-right-radius: 5px;
    border: 1px solid white;
    border-left: none;
  }
}
