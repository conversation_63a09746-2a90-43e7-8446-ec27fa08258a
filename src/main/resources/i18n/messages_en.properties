delete=Delete
neubauer=Neubauer
dashboard.title=Dashboard
user.userlist.title=Users
user.create.title=Create User
user.edit.title=Edit User
user.username=Username
user.usernameCriteria=For example JDO for <PERSON>
user.password=Password
user.passwordCriteria=At least one upper case letter, one lower case letter and one number, 8-40 characters
user.firstName=First Name
user.lastName=Last Name
user.phoneNumber=Phone Number
user.status=Status
user.status.active=Active
user.status.inactive=Inactive
user.status.all=All
user.enable=Enable user
user.disable=Disable user
user.disable.confirm=Do you really want to deactivate this user?
user.role=Role
user.create.success=User was created successfully.
user.edit.success=User was edited successfully.
user.enable.success=User was enabled successfully.
user.disable.success=User was disabled successfully.
login.title=Login
login.subtitle=Login with your account
login.username=Username
login.password=Password
logout.message=You have been logged out.
logout.title=Logout
errors.generic=An error has occurred.
errors.dateformat=The date is not formatted correctly.
errors.timeformat=The time is not formatted correctly.
errors.input.tooLong=The text is too long.
errors.login.accountLocked=Your account has been disabled.
errors.login.authFailed=Authentication failed.
errors.login.invalidCredentials=The credentials you provided are invalid.
errors.notFound=The requested resource could not be found.
errors.issue.notFound=The issue could not be found.
errors.issue.note.format=Could not be saved. Check the format of your input.
errors.password.invalid.generic=Password is invalid.
errors.password.invalid.tooShort=Password is too short.
errors.password.invalid.tooLong=Password is too long.
errors.password.invalid.criteriaNotMet=Password does not meet criteria.
errors.missingPermission=You do not have the required permission to perform this action.
errors.user.duplicateUsername=The username already exists.
errors.user.notFound=The user could not be found.
errors.username.notBlank=Username must not be empty.
errors.password.notBlank=Password must not be empty.
errors.firstName.notBlank=First name must not be empty.
errors.lastName.notBlank=Last name must not be empty.
errors.absence.start_before_end=The end date has to be after the start date.
errors.assignment.tooShort=The assignment is too short. Please choose a longer duration.
role.admin=Administrator
role.supervisor=Supervisor
role.technician=Technician
role.assistant=Assistant
scheduler.title=Resourceplanning
scheduler.technician.title=Employee
scheduler.technician.navigation=Employee
scheduler.assistant.title=Assistants
issue.detail.address=Address
issue.detail.contactPerson=Contact Person
issue.detail.suggestedDate=Suggested Date
issue.detail.description=Description
issue.detail.name=Technician Name
issue.detail.date=Date
issue.detail.note=Note
issue.detail.note.success=The note was successfully updated.
issue.detail.note.error=The note could not be updated. Please reload this page.
issue.recurring.frequency=Repetitions
issue.recurring.repetitions=Number of recurring issues
issue.delete=Delete
issue.delete.error=An error has occurred while deleting the appointment.
errors.recurringissue.repetitions.notBlank=Number cannot be empty.
errors.recurringissue.repetitions.range=Number has to be between 1 - 99.
errors.recurringissue.frequency.range=Choose the frequency you want to use.
recurringissue.repetitions.range=1-99
recurringissue.delete=Delete empty entries
recurringissue.none=No selection
recurringissue.weekly=Weekly
recurringissue.biweekly=Bi-weekly
recurringissue.monthly=Monthly
recurringissue.twomonthly=2 Monthly
recurringissue.threemonthly=3 Monthly
recurringissue.fourmonthly=4 Monthly
recurringissue.fivemonthly=5 Monthly
recurringissue.halfyearly=Half yearly
recurringissue.yearly=Yearly
recurringissues.create.success=Recurring issue was successfully added.
recurringissues.create=Create Recurring Issue
recurringissues.createMore=Add more
recurringissues.subissue.success=Subissue was successfully added.
recurringissues.subissue.duplicate=Cannot assign single issue to multiple dates.
recurringissues.subissue.unassign=Should the link to the recurring issue be dissolved?
recurringissues.subissue.delete=Should the assignment be deleted?
recurringissues.placeholder.delete=Should the placeholders be deleted?
issue.recurring.subissue=This issue is part of a recurring issue.
issue.recurring.date=Dates
issue.recurring.issue=Issues
issue.recurring.technician=Technician
issue.recurring.unassign.success=Dissolved link to recurring issue.
issue.recurring.unassign.error=Couldn't dissolve link to recurring issue.
issue.recurring.unassign=Dissolve link to recurring issue
issue.assignment.edit.success=Successfully saved your changes to the issue assignment.
status.new=New
status.reopened=Reopened
status.planned=Planned
status.accepted=Accepted
status.not_started=Not Started
status.done_technician=Done
status.closed=Closed
status.all=All
back=Back
save=Save
issues.list.title=Issues
issues.list.placeholder=Search for issue...
issues.externalId=Issue
issues.address=Customer
issues.contactPerson=Contact Person
issues.description=Description
issues.status=Status
issues.latestAssignmentDateStart=Date
issue.history.employee=Employee
issue.history.date=Date
issue.history.status=Status
issue.history.note=Note
issue.history.accepted=The issue was accepted.
issue.history.putBack=The issue was put back.
issue.history.revoke=The issue assignment of {0} was revoked.
issue.history.statusChange=The issue status was changed.
issue.documents.type=Type
issue.documents.type.OFFER=Offer
issue.documents.type.RECEIPT=Receipt
issue.documents.title=Title
issue.documents.date=Date
issue.documents.download=Download
issue.documents.emptyList=No documents available
technicians.list.placeholder=Search for technician...
absence.type.doctor=Doctor Appointment
absence.type.vocationalSchool=Vocational School
absence.type.holiday=Public Holiday
absence.type.dayOff=Day Off
absence.type.homeEarly=Gone Home Early
absence.type.militaryService=Military Service
absence.type.inventory=Inventory
absence.type.sickLeave=Sick Leave
absence.type.finalExam=Final Apprenticeship Examination
absence.type.finalExamPrepCourse=Final Apprenticeship Examination Preparation Course
absence.type.schoolHolidays=School Holidays
absence.type.seminar=Seminar
absence.type.other=Other Reason
absence.type.vacation=Vacation
absence.type.compTime=Compensatory Time
absence.type.civilService=Civil Service
absences.title=Absences
absence.title=Absence
absence.type.title=Absence Types
absence.note=Note
absence.startDate=Start Date
absence.endDate=End Date
absence.startTime=Start Time
absence.endTime=End Time
absence.success=Absence was edited sucessfully.
images.nothingfound=No Images here.
images.delete.confirmation=Are you sure you want to delete the selected pictures?
images.delete=Delete pictures
tab.assignment=Assignment
tab.detail=Details
tab.images=Images
tab.documents=Documents
tab.smartbricks=Smartbricks
tab.contextMenu.open=Open
tab.contextMenu.delete=Delete
tab.contextMenu.delete.confirmation=Do you really want to delete this file?
tab.contextMenu.delete.error=An error occurred while deleting the file
requestImages=Request Pictures
tab.history=History
tab.recurringIssue=Recurring Issue
selectDeselectAll=(De)Select
images.template.oneone=PDF Download - 1 picture per page
images.template.onetwo=PDF Download - 2 pictures per page
images.template.twotwo=PDF Download - 4 pictures per page
images.template.threethree=PDF Download - 9 pictures per page
images.template.download =JPG Download - Download pictures separately
images.download=Download
images.move=Move
images.move.label=Move pictures
images.move.confirm=Are you sure you want to move the selected pictures?
print=Print
print.date=Date
print.title=Resourceplan
print.technician=Technician
print.assistant=Assistant
print.time=Time
print.absence=Absence
print.issue=Issue
print.address=Address
print.footer.description=Printed on
print.footer.page=Page
print.footer.of=of
form.invalid=Form not filled correctly.
errors.id.notEmpty=ID is required.
errors.template.notEmpty=Template is required.
errors.image.notEmpty=Images are requried.
errors.image.move.choose_issue=Please choose an issue.
errors.image.notFound=Unable to find the picture.
images.selected=Images selected
images.downloadOptions=Download options
images.requested=Images requested.
sms.send.success=Images requested.
sms.send.error=Images could not be requested.
status.change.success=Status changed successfully.
status.change.error=Status could not be changed.
status.reopened.button=Reopen Issue
status.closed.button=Complete Issue
startDate=Start Date
endDate=End Date
startTime=Start Time
endTime=End Time
placeholder.title=Planned Issue
smartbricks.title=Smartbricks
smartbricks.authorization.checkOngoing=Checking authorization status...
smartbricks.notAuthorized=In order to access this feature you need to additionally log in to your smartbricks account. You can do this <a href="/admin/smartbricks/authorization-page" target="_top">here</a>.
smartbricks.info=In order to access all features on this platform you need to additionally log in to your smartbricks account.
