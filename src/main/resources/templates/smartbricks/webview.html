<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
</head>

<body>
<section layout:fragment="content" class="container">
    <div class="gr-12 no-gutter">
        <script th:inline="javascript">
            const performAuthenticationCheck = async () => {
                const oauthInfos = /*[[${oauth2Information}]]*/ {
                    // placeholders - they will be replaced by thymeleaf but specifying the structure here for
                    // the editor to understand it
                    domain: 'default',
                    expectedState: 'default',
                    authorizationUrl: 'default',
                };

                // create hidden iframe to call the authorization url to get a response from the idp if
                // the user has a valid session
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.setAttribute('src', oauthInfos.authorizationUrl);
                document.body.appendChild(iframe);

                // Timeout after 10 seconds of no message receival
                const timeoutId = setTimeout(() => {
                    showContent('unauthorized-template');
                }, 10000);

                const messageHandler = (event) => {
                    if (event.origin !== oauthInfos.domain || !event.data || event.data.type !== 'authorization_response') {
                        // not a message we are interested in
                        return;
                    }

                    const response = event.data.response;

                    if (response.state !== oauthInfos.expectedState) {
                        // potential hijacking attempt
                        showContent('unauthorized-template');
                        return;
                    }

                    if (response.code) {
                        showContent('authorized-template');
                    } else {
                        showContent('unauthorized-template');
                    }
                };

                const cleanup = () => {
                    clearTimeout(timeoutId);
                    window.removeEventListener('message', messageHandler);
                    if (document.body.contains(iframe)) {
                        document.body.removeChild(iframe);
                    }
                };

                const showContent = (templateId) => {
                    cleanup();

                    const template = document.getElementById(templateId);
                    const contentTarget = document.getElementById('content-target');
                    const content = template.content.cloneNode(true);
                    contentTarget.innerHTML = '';
                    contentTarget.appendChild(content);
                }

                window.addEventListener('message', messageHandler);
            }

            window.addEventListener('load', performAuthenticationCheck);
        </script>

        <div id="content-target">
            <div class="padding-left-50 padding-right-50 padding-top-20">
                <p th:utext="#{smartbricks.authorization.checkOngoing}"></p>
            </div>
        </div>

        <template id="authorized-template">
            <iframe th:src="${smartbricksWebviewUrl}" frameborder="0" width="100%" height="498"></iframe>
        </template>

        <template id="unauthorized-template">
            <div class="padding-left-50 padding-right-50 padding-top-20">
                <p th:utext="#{smartbricks.notAuthorized}"></p>
            </div>
        </template>
    </div>
</section>
</body>
</html>
