<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" layout:decorate="~{popup-template}" class="y-hidden x-hidden">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,400i,600,600i,700,700i" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/assets/css/grid.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/styles.css}">
</head>

<body class="y-hidden">
    <section layout:fragment="content">
        <div th:class="'image-grid-'+${type}">
            <div class="row">
                <div class="gr-12">
                    <div class="row">
                        <div class="gr-12 padding-left-50 padding-right-65 padding-top-15 padding-bottom-10">
                            <div class="alert alert-info">
                                <small>These features are only available for images uploaded via the app. Smartbricks images can only be downloaded through clicking on them</small>
                            </div>
                        </div>
                        <div class="gr-12 padding-top-20 padding-left-50 padding-right-65 padding-bottom-20 fixed-navbar">
                            <div class="row">
                                <div class="col-xs-5ths">
                                    <div class="row padding-bottom-15">
                                        <div class="gr-12">
                                            <small class="image-count">0</small> <small th:text="#{images.selected}">Bilder ausgewählt</small>
                                        </div>
                                    </div>

                                    <button class="select-all btn-popup" th:text="#{selectDeselectAll}">Select / deselect all</button>
                                </div>
                                <div  class="col-xs-2-5ths">
                                    <div class="row padding-bottom-15">
                                        <div class="gr-12">
                                            <small th:text="#{images.downloadOptions}">Download Optionen</small>
                                        </div>
                                    </div>
                                    <form class="with-selection" role="form" th:action="${downloadImagesUrl}" method="POST">
                                        <div class="row padding-bottom-15">
                                            <div class="gr-12">
                                                <select name="template" id="template" class="template-select">
                                                    <option value="ONEONE" th:text="#{images.template.oneone}">Ein Bild je Seite</option>
                                                    <option value="ONETWO" th:text="#{images.template.onetwo}">Zwei Bilder je Seite</option>
                                                    <option value="TWOTWO" th:text="#{images.template.twotwo}">Vier Bilder je Seite</option>
                                                    <option value="THREETHREE" th:text="#{images.template.threethree}">Neun Bilder je Seite</option>
                                                    <option value="JPG" th:text="#{images.template.download}">Bilder einzeln herunterladen</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row padding-bottom-15">
                                            <div class="gr-12">
                                                <input type="submit" name="submit" th:value="#{images.download}" class="btn-popup">
                                                <input type="hidden" name="id" th:value="${id}">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-xs-2-5ths">
                                    <div class="row padding-bottom-15">
                                        <div class="gr-12">
                                            <small th:text="#{images.move.label}">Bilder Verschieben</small>
                                        </div>
                                    </div>
                                    <form id="move-images-form" class="with-selection" role="form" th:action="${moveImagesUrl}" method="POST">
                                        <div class="row padding-bottom-15">
                                            <div class="gr-12">
                                                <input name="targetIssueId" id="targetIssueId" class="target-issue-select full-width" />
                                            </div>
                                        </div>
                                        <div class="row padding-bottom-15">
                                            <div class="gr-12">
                                                <input type="submit" name="submit" th:value="#{images.move}" class="btn-popup">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="gr-12 padding-left-60 padding-right-60 image-content" th:if="${#lists.isEmpty(images) && #lists.isEmpty(smartbricksImages)}" th:text="#{images.nothingfound}"></div>
                <div class="gr-12 padding-left-60 padding-right-60 image-content" th:if="${not #lists.isEmpty(images) || not #lists.isEmpty(smartbricksImages)}">
                    <div class="row">
                        <div class="col-xs-5ths" th:each="image : ${images}">
                            <div>
                                <a th:href="${image.imageUrl}" download th:id="${image.id}"></a>
                                <div class="thumbnail" th:style="'background-image: url('+${image.thumbnailUrl}+')'">
                                    <div class="checkbox">
                                        <input class="checkbox downloadCheckbox" type="checkbox" name="image" th:value="${image.id}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-5ths" th:each="smartbricksImage : ${smartbricksImages}">
                            <div>
                                <a th:href="@{/admin/smartbricks/download(signedUrl=${smartbricksImage.signedUrl}, filename=${smartbricksImage.name})}">
                                    <div class="smartbricks-image" th:style="'background-image: url('+${smartbricksImage.signedUrl}+');'">
                                        <div class="smartbricks-hint">
                                            <span th:text="#{smartbricks.title}"></span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="position-fixed-iframe background-white">
            <div class="gr-6 no-gutter text-left padding-top-20 padding-bottom-10">
                <form class="form-horizontal with-selection block-inline margin-right-10" role="form" th:action="${deleteImagesUrl}"
                      method="POST" onClick="return confirm(translation.imagesDeleteConfirmation)">
                    <input type="submit" name="submit" th:value="#{images.delete}" class="btn secondary danger-font-color">
                </form>
            </div>
            <div class="gr-6 no-gutter text-right padding-top-20 padding-bottom-10">
                <form class="form-horizontal block-inline"  method="post" th:action="${requestImagesUrl}">
                    <input class="btn secondary" type="submit" th:value="#{requestImages}">
                </form>
            </div>
        </div>

        <div th:class="'alert-fixed '+${type}" style="display: none" th:if="${smsSuccess}">
            <div class="alert alert-success" th:text="${smsSuccess}">Bilder wurden angefordert.</div>
        </div>

        <div th:class="'alert-fixed '+${type}" style="display: none" th:if="${smsError}">
            <div class="alert alert-danger" th:text="${smsError}">Bilder wurden angefordert.</div>
        </div>

        <script th:inline="javascript">
            $(document).ready(function () {

                $('#targetIssueId').kendoComboBox({
                    dataTextField: "externalId",
                    dataValueField: "id",
                    dataSource: new kendo.data.DataSource({
                        serverPaging: false,
                        serverSorting: false,
                        serverFiltering: false,
                        transport: {
                            read: {
                                url: baseURL + '/issues/open',
                                dataType: 'json',
                                data: {

                                }
                            }
                        },
                        sortable: false,
                        sort: { field: 'externalId', dir: 'asc' },
                    }),
                    filter: "contains",
                    suggest: true,
                    clearButton: false,
                    noDataTemplate: /*[[#{errors.issue.notFound}]]*/,
                });

                $("input[name='targetIssueId_input']" ).click(function() {
                    $('#targetIssueId').data('kendoComboBox').open();
                });

                $('#move-images-form').submit(function(e) {
                    var comboBox = $('#targetIssueId').data('kendoComboBox')
                    var selectedIssueId = comboBox.value()
                    var openIssues = comboBox.dataSource._data

                    for (var i = 0; i < openIssues.length; i++) {
                        if (openIssues[i].id == selectedIssueId) {
                            return confirm(/*[[#{images.move.confirm}]]*/)
                        }
                    }

                    e.preventDefault()
                    e.stopImmediatePropagation()

                    alert(/*[[#{errors.image.move.choose_issue}]]*/)

                    return false
                });

                $('.select-all').on('click', function () {
                    if ($(this).hasClass('active')) {
                        $(this).removeClass('active')
                        $('.checkbox').prop('checked', false);
                        $('.checkbox').each(function (i, ele) {
                            var id = $(this).val()
                            uncheck(id)
                        })
                    } else {
                        $(this).addClass('active')
                        $('.checkbox').prop('checked', true);
                        $('.checkbox').each(function (i, ele) {
                            var id = $(this).val()
                            check(id)
                        })
                    }
                    countImages()
                })
                $('.checkbox').on('click', function () {
                    var id = $(this).val()
                    if (this.checked) {
                        check(id)
                    } else {
                        uncheck(id)
                    }
                    countImages()
                })

                $('form.with-selection').on('submit', function (e) {
                    if ($(this).find('[name=images]').length == 0) {
                        alert(/*[[#{errors.image.notEmpty}]]*/)
                        e.preventDefault()
                        e.stopImmediatePropagation()
                    }
                    else {
                        if(document.getElementById("template").value === "JPG") {
                            downloadImages()

                            //in order to stop the form from submitting / calling backend
                            e.preventDefault()
                            e.stopImmediatePropagation()
                        }
                    }
                })

                var check = function(id) {
                    if ($('form.with-selection #image-' + id).length < 1 && id != null && id != "") {
                        $('form.with-selection').append('<input id="image-' + id + '" type="hidden" name="images" value="' + id + '">')
                    }
                }

                var uncheck = function(id) {
                    if ($('form.with-selection #image-' + id).length > 0) {
                        $('form.with-selection #image-' + id).remove()
                    }
                }

                var countImages = function () {
                    var imageCount = $('.checkbox:checked').length
                    $('.image-count').text(imageCount)
                }

                if($('.alert-fixed').length > 0) {
                    $('.alert-fixed').fadeIn();
                    setTimeout(function() {
                        $('.alert-fixed').fadeOut();
                    }, 3000)
                    window.history.pushState(null, null, window.location.pathname);
                }

                var downloadImages = function () {
                    $('.downloadCheckbox').each(function () {
                        if(this.checked) {
                            var id = $(this).val()
                            var a = document.getElementById(id)
                            a.click()
                        }
                    })
                }
            })
        </script>
    </section>
</body>

</html>