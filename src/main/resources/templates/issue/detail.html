<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      xmlns:bpo="http://www.w3.org/1999/xhtml"
      layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issue.detail.title}"></title>
</head>

<body layout:fragment="content">
<section class="issue-detail full-height">
    <div class="container padding-left-50 padding-right-50 padding-top-20 padding-bottom-10">
        <div class="information">
            <div class="row">
                <div class="gr-12">
                    <div class="gr-6 no-gutter">
                        <div class="flexible-container">
                            <h1 class="header" th:text="${issue.externalId}">AS180455</h1>
                            <div id="issuescan"></div>
                            <a id="scan-upload-button" href="#"><img src="/assets/images/ic_add.png"></a>
                            <form id="scan-form" method="post" role="form" th:action="${scanUploadUrl}"
                                  enctype="multipart/form-data">
                                <input id="scan-upload" name="scan" type="file" accept="application/pdf, image/jpeg"
                                       hidden>
                            </form>
                        </div>
                        <div id="scan-error" class="help-block padding-top-10 padding-bottom-10" style="display: none">
                            Beim Hochladen des Dokuments ist ein Fehler aufgetreten.
                        </div>
                        <div id="scan-delete-error" class="help-block padding-top-10 padding-bottom-10"
                             style="display: none">
                            <span th:text="#{tab.contextMenu.delete.error}">Error</span>
                        </div>
                    </div>

                    <div class="gr-6 no-gutter text-right">
                        <div class="pull-right">
                            <div class="badge"
                                 th:style="'background: linear-gradient(to right, ' + ${gradientStartColor} + ', ' + ${gradientEndColor} +'); color: #FFFFFF;'"
                                 th:text="${issue.status}">
                                PLANNED
                            </div>
                        </div>
                    </div>
                    <div class="gr-12 no-gutter">
                        <ul class="tabs">
                            <li th:if="${issue.showAssignmentTab}"><a href="#tab1"
                                                                      th:classappend="${issue.preselectAssignmentTab} ? active"
                                                                      th:text="#{tab.assignment}"
                                                                      th:if="${issue.showAssignmentTab}">Assignment</a>
                            </li>
                            <li><a href="#tab2" th:classappend="${issue.preselectDetailTab} ? active"
                                   th:text="#{tab.detail}">Details</a></li>
                            <li><a href="#tab3" th:text="#{tab.images}">Bilder</a></li>
                            <li><a href="#tab4" th:text="#{tab.documents}">Dokumente</a></li>
                            <li><a href="#tab5" th:text="#{tab.history}">Historie</a></li>
                            <li th:if="${issue.showRecurringIssueTab}"><a href="#tab6"
                                                                          th:classappend="${issue.preselectRecurringIssueTab} ? active"
                                                                          th:text="#{tab.recurringIssue}"
                                                                          th:if="${issue.showRecurringIssueTab}">Assignment</a>
                            </li>
                            <li th:if="${issue.smartbricksProjectId != null}">
                                <a href="#tab7" th:text="#{tab.smartbricks}">Bauinfos (Smartbricks)</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr style="margin: 0;">

    <div id="tab1" class="tab" th:classappend="${issue.preselectAssignmentTab} ? active"
         th:if="${issue.showAssignmentTab}">
        <form class="form-horizontal" role="form" th:action="${issue.assignmentDetailFormSubmitUrl}"
              th:object="${assignmentDetail}" method="POST">

            <div class="container padding-top-36 padding-right-50 padding-left-50">
                <div class="row margin-bottom-30">
                    <div class="gr-12">
                        <small th:text="#{role.technician}">Technician</small>
                        <div class="margin-top-5" th:text="${assignment.technicianName}"></div>
                    </div>
                </div>

                <div class="row margin-bottom-30">
                    <div class="gr-12">
                        <div class="gr-6 no-gutter">
                            <small th:text="#{startDate}">#{absence.startDate}</small>
                            <div class="row">
                                <div class="gr-12 no-gutter margin-top-5">
                                    <input class="datePicker" th:field="*{startDate}"/>
                                    <small th:if="${#fields.hasErrors('startDate')}"
                                           class="help-block padding-top-10 padding-bottom-10"
                                           th:errors="*{startDate}"></small>
                                </div>
                            </div>
                        </div>

                        <div class="gr-6 no-gutter">
                            <small th:text="#{endDate}">#{absence.endDate}</small>
                            <div class="row">
                                <div class="gr-12 no-gutter margin-top-5">
                                    <input class="datePicker" th:field="*{endDate}"/>
                                    <small th:if="${#fields.hasErrors('endDate')}"
                                           class="help-block padding-top-10 padding-bottom-10"
                                           th:errors="*{endDate}"></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row margin-bottom-30">
                    <div class="gr-12">
                        <div class="gr-6 no-gutter">
                            <small th:text="#{startTime}">#{absence.startTime}</small>
                            <div class="row">
                                <div class="gr-12 no-gutter margin-top-5">
                                    <input class="timePicker" th:field="*{startTime}"/>
                                    <small th:if="${#fields.hasErrors('startTime')}"
                                           class="help-block padding-top-10 padding-bottom-10"
                                           th:errors="*{startTime}"></small>
                                </div>
                            </div>
                        </div>
                        <div class="gr-6 no-gutter">
                            <small th:text="#{endTime}">#{absence.endTime}</small>
                            <div class="row">
                                <div class="gr-12 no-gutter margin-top-5">
                                    <input class="timePicker" th:field="*{endTime}"/>
                                    <small th:if="${#fields.hasErrors('endTime')}"
                                           class="help-block padding-top-10 padding-bottom-10"
                                           th:errors="*{endTime}"></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="position-fixed background-white">
                    <div class="gr-12 no-gutter text-right padding-right-50 padding-left-50 padding-top-20 padding-bottom-10">
                        <button type="submit" class="btn primary " th:text="#{save}"></button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div id="tab2" class="tab" th:classappend="${issue.preselectDetailTab} ? active">
        <div class="container padding-top-36 padding-right-50 padding-left-50">
            <div class="gr-12 margin-bottom-30">
                <div class="row">
                    <div class="gr-6 no-gutter padding-right-50">
                        <small th:text="#{issue.detail.address}">#{issue.detail.address}</small>
                        <div class="margin-top-5" th:text="${issue.address}"></div>
                    </div>
                    <div class="gr-3 no-gutter padding-right-50">
                        <small th:text="#{issue.detail.contactPerson}">#{issue.detail.contactPerson}</small>
                        <div class="margin-top-5" th:text="${issue.contactPerson}"></div>
                    </div>
                    <div class="gr-3 no-gutter">
                        <small th:text="#{issue.detail.suggestedDate}">#{issue.detail.suggestedDate}</small>
                        <div class="margin-top-5" th:text="${issue.suggestedDate}"></div>
                    </div>
                </div>
            </div>
            <div class="gr-12 no-gutter">
                <div class="row">
                    <div class="gr-6 no-gutter margin-bottom-50 padding-right-50">
                        <small th:text="#{issue.detail.description}">#{issue.detail.description}</small>
                        <div class="margin-top-5" th:text="${issue.description}"></div>
                    </div>

                    <div class="gr-6 no-gutter">
                        <div class="gr-12 no-gutter" th:each="assignment : ${issue.assignments}">
                            <div class="row">
                                <div class="gr-6 no-gutter margin-bottom-30">
                                    <small th:text="#{issue.detail.date}">#{issue.detail.date}</small>
                                    <div class="margin-top-5" th:text="${assignment.startDateFormatted}"></div>
                                </div>
                                <div class="gr-6 no-gutter margin-bottom-30 padding-right-50">
                                    <small th:text="#{issue.detail.name}">#{issue.detail.name}</small>
                                    <div class="margin-top-5" th:text="${assignment.fullNameOfTechnician}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gr-12 no-gutter">
                    <div class="gr-12 no-gutter margin-bottom-30">
                        <form id="note-form" class="form-horizontal" role="form"
                              th:action="${issue.issueDetailFormSubmitUrl}" th:object="${issueDetailNote}" method="POST"
                              bpo:disabled-without-permission="EDIT_ISSUE_DETAIL_NOTE">
                            <small th:text="#{issue.detail.note}">#{issue.detail.note}</small>
                            <input type="hidden" th:field="*{initialNoteHash}"/>
                            <div class="margin-top-5">
                                <textarea class="form-control" th:field="*{note}" rows="5"></textarea>
                                <small th:if="${#fields.hasErrors('note')}"
                                       class="help-block padding-top-10 padding-bottom-10" th:errors="*{note}"></small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="position-fixed background-white padding-left-50 padding-right-50">
                <div class="gr-4 no-gutter text-left padding-top-20 padding-bottom-10">
                    <form id="delete-assignment-form" class="form-horizontal block-inline margin-right-10"
                          th:action="${deleteAssignmentUrl}"
                          th:method="DELETE" th:if="${issue.assignmentId != null}"
                          onClick="return confirm(translation.deleteAssignment)"
                          th:data-value="${deleteAssignmentUrl}">
                        <input type="hidden" th:name="assignment" th:value="${issue.assignmentId}"/>
                        <button type="submit" class="btn secondary danger-font-color" th:text="#{issue.delete}"></button>
                    </form>
                    <button bpo:required-permission="PRINT_ISSUE_DETAILS" id="note-form-print-button"
                            class="btn secondary" th:text="#{print}"></button>
                    <div id="delete-error"
                         class="help-block padding-top-10"
                         style="display: none"
                         th:text="#{issue.delete.error}">
                    </div>
                </div>
                <div class="gr-8 no-gutter text-right padding-top-20 padding-bottom-10">
                    <form class="form-horizontal block-inline margin-right-10" th:action="${reopenIssueUrl}"
                          method="POST" th:if="${displayReopenIssueButton}" bpo:required-permission="REOPEN_ISSUE">
                        <input type="hidden" th:name="assignment" th:value="${issue.assignmentId}"/>
                        <button type="submit" class="btn secondary" th:text="#{status.reopened.button}"></button>
                    </form>
                    <form class="form-horizontal block-inline margin-right-10" th:action="${closeIssueUrl}"
                          method="POST" th:if="${displayCloseIssueButton}" bpo:required-permission="CLOSE_ISSUE">
                        <input type="hidden" th:name="assignment" th:value="${issue.assignmentId}"/>
                        <button type="submit" class="btn secondary" th:text="#{status.closed.button}"></button>
                    </form>
                    <button id="note-form-submit-button" type="submit" class="btn primary" th:text="#{save}"></button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div id="tab3" class="tab tab-iframe">
        <iframe th:src="@{/admin/issues/{issueId}/images(issueId=${issueId})}" frameborder="0" width="100%" height="560"
                class="image-iframe"></iframe>
    </div>

    <div id="tab4" class="tab">
        <iframe th:src="@{/admin/issues/{issueId}/documents(issueId=${issueId})}" frameborder="0" width="100%"
                height="461"></iframe>
    </div>

    <div id="tab5" class="tab">
        <iframe th:src="@{/admin/issues/{issueId}/history(issueId=${issueId})}" frameborder="0" width="100%"
                height="461"></iframe>
    </div>

    <div id="tab6" class="tab grey-background full-height" th:classappend="${issue.preselectRecurringIssueTab} ? active"
         th:if="${issue.showRecurringIssueTab}">
        <div class="container">
            <div th:if="${isAssignedSubIssue}"
                 class="gr-12 margin-bottom-30 padding-right-50 padding-left-50 padding-top-36"
                 th:text="#{issue.recurring.subissue}"></div>
            <div class="gr-12 padding-top-36 padding-right-50 padding-left-50 tab5-sticky-header"
                 th:if="${!isAssignedSubIssue}">
                <form id="recurring-issue-form" class="form-horizontal" role="form"
                      th:action="${issue.recurringIssueSubmitUrl}" th:object="${recurringIssue}" method="POST">
                    <div class="gr-5 no-gutter margin-bottom-25 padding-right-50">
                        <small th:text="#{issue.recurring.frequency}">Wiederholungen</small>
                        <div class="margin-top-10">
                            <select id="frequency" class="template-select" th:field="*{frequency}">
                                <option th:each="frequency : ${frequencies}"
                                        th:value="${frequency.ordinal}" th:text="#{${frequency.localizationKey}}"
                                        th:selected="${frequency.ordinal} == *{frequency}">
                                </option>
                            </select>
                            <small th:if="${#fields.hasErrors('frequency')}"
                                   class="help-block padding-top-10 padding-bottom-10" th:errors="*{frequency}"></small>
                        </div>
                    </div>
                    <div class="gr-3 no-gutter margin-bottom-25 padding-right-50">
                        <small th:text="#{issue.recurring.repetitions}">Ende nach</small>
                        <div class="gr-12 no-gutter margin-top-10">
                            <input type="number" class="input-popup" th:field="*{repetitions}"
                                   th:placeholder="#{recurringissue.repetitions.range}"/>
                            <small th:if="${#fields.hasErrors('repetitions')}"
                                   class="help-block padding-top-10 padding-bottom-10"
                                   th:errors="*{repetitions}"></small>
                        </div>
                    </div>
                    <div class="gr-4 no-gutter margin-top-20">
                        <button id="recurring-issue-form-submit-button" type="submit" class="btn primary"
                                onclick="this.disabled = true"
                                th:text="${isRecurringIssue}
                                            ? #{recurringissues.createMore}
                                            : #{recurringissues.create}"></button>
                    </div>
                </form>
            </div>
            <div class="gr-12 margin-bottom-30 padding-right-50 padding-left-50 "
                 th:classappend="${isAssignedSubIssue} ? '' : 'tab5-content'"
                 th:if="${isRecurringIssue || isAssignedSubIssue}">
                <div class="gr-12 no-gutter">
                    <div class="row grid_header grid_row_border">
                        <div class="gr-3 grid_cell">
                            <small th:text="#{issue.recurring.date}"></small>
                        </div>
                        <div class="gr-3 grid_cell">
                            <small th:text="#{issue.recurring.issue}"></small>
                        </div>
                        <div class="gr-6 grid_cell">
                            <small th:text="#{issue.recurring.technician}"></small>
                        </div>
                    </div>

                    <div class="gr-12 no-gutter" th:if="${recurringMainIssue != null}">
                        <div class="row grid_row_border">
                            <div class="gr-3 grid_cell">
                                <a th:href="${recurringMainIssue.dateUrl}" target="_top"
                                   th:text="${recurringMainIssue.date}"></a>
                            </div>
                            <div class="gr-3 grid_cell bold">
                                <a th:href="${recurringMainIssue.issueUrl}"
                                   th:text="${recurringMainIssue.issueNumber}"></a>
                            </div>
                            <div class="gr-6 grid_cell">
                                <div th:text="${recurringMainIssue.technicianName}"></div>
                            </div>
                        </div>
                    </div>
                    <form th:if="!${isAssignedSubIssue}" id="assign-subissue-form" class="form-horizontal" role="form"
                          th:action="${issue.assignSubIssueFormSubmitUrl}" method="POST"
                          th:object="${subIssueAssignment}">
                        <div class="gr-12 no-gutter" th:each="recurringIssue, row : ${recurringIssues}">
                            <div class="row" th:classappend="!${row.last} ? grid_row_border">
                                <div class="gr-3 grid_cell">
                                    <a th:href="${recurringIssue.dateUrl}" target="_top"
                                       th:text="${recurringIssue.date}"></a>
                                </div>
                                <div class="gr-3 grid_cell"
                                     th:classappend="${recurringIssue.issueNumber == null} ? padding-tb-8">
                                    <a th:href="${recurringIssue.issueUrl}"
                                       th:if="${recurringIssue.issueNumber != null}"
                                       th:text="${recurringIssue.issueNumber}"></a>
                                    <input th:id="'subIssueAssignment-' + ${recurringIssue.id}"
                                           th:if="${recurringIssue.issueNumber == null}"
                                           class="subIssueAssignment target-issue-select full-width"
                                           th:field="*{subIssues['__${recurringIssue.id}__']}"/>
                                </div>
                                <div class="gr-6 grid_cell">
                                    <div th:text="${recurringIssue.technicianName}"></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div th:if="${isAssignedSubIssue == false && isRecurringIssue}" class="position-fixed background-white">
                <div class="gr-8 no-gutter padding-right-50 padding-left-50 padding-top-20 padding-bottom-10">
                    <form class="form-horizontal with-selection block-inline margin-right-10" role="form"
                          th:action="@{|/admin/api/v1/technicians/assignments/deleteIssuePlaceholder/${issueId}${assignment != null ? '/' + assignment.id : ''}|}"
                          method="GET" onClick="return confirm(translation.deleteIssuePlaceholder)">
                        <input type="submit" name="submit" th:if="${isRecurringIssue}"
                               th:value="#{recurringissue.delete}" class="btn secondary danger-font-color">
                    </form>
                </div>
                <div class="gr-4  text-right no-gutter padding-right-50 padding-left-50 padding-top-20 padding-bottom-10">
                    <button id="assign-subissue-form-submit-button" type="submit" class="btn primary"
                            th:if="${isRecurringIssue}" th:text="#{save}"></button>
                </div>
            </div>
            <div th:if="${isAssignedSubIssue && issue.assignmentId != null}" class="position-fixed background-white">
                <div class="gr-12 no-gutter text-right padding-top-20 padding-bottom-10 padding-right-50">
                    <form class="form-horizontal block-inline margin-right-10" th:action="${unassignSubIssueUrl}"
                          method="POST" th:if="${isAssignedSubIssue}" onClick="return confirm(translation.unassignSubIssue)">
                        <input type="hidden" th:name="assignment" th:value="${issue.assignmentId}"/>
                        <button type="submit" class="btn secondary" th:text="#{issue.recurring.unassign}"></button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="tab7" class="tab" th:if="${issue.smartbricksProjectId != null}">
        <iframe th:src="@{/admin/smartbricks/webview(smartbricksProjectId=${issue.smartbricksProjectId})}"
                frameborder="0" width="100%" height="498"></iframe>
    </div>


    <div id="contextMenu" class="context-menu" style="display:none">
        <ul id="contextMenuList">
            <li id="contextMenuOpenListItem" style="display: none;">
                <a th:text='#{tab.contextMenu.open}'>Öffnen</a>
            </li>
            <li id="contextMenuDeleteListItem" style="display: none;">
                <form id="scan-delete-form" method="POST" role="form">
                    <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>
                    <a class="red-text" th:text='#{tab.contextMenu.delete}'>Löschen</a>
                </form>
            </li>
        </ul>
    </div>
</section>
<script th:inline="javascript">

    $(document).ready(function () {
        if ($('.alert-fixed').length > 0) {
            $('.alert-fixed').fadeIn();
            setTimeout(function () {
                $('.alert-fixed').fadeOut();
            }, 3000)
        }
        var issueId = /*[[${issueId}]]*/;
        var issueScanUrl = /*[[${scanUploadUrl}]]*/
        var issueScanDeleteBaseUrl = /*[[${scanDeleteBaseUrl}]]*/
        var issueRetrieveUrl = '/issue/scan/'

        document.onclick = hideMenu;

        function isManualUpload(key) {
            return key.split(' ')[1] === "upload";
        }

        function hideMenu() {
            $("#contextMenu").css({display: 'none'});
        }

        function showIssueScanContextMenu(event, issueLink, key) {
            if (document.getElementById("contextMenu").style.display == "block") {
                hideMenu();
            } else {
                var openItem = $("#contextMenuOpenListItem");
                openItem.css({display: "block", borderBottom: "1px solid #B2B2B2"});
                openItem
                    .children()
                    .first()
                    .attr('href', issueLink + key)
                    .attr('target', '_blank');

                var deleteItem = $("#contextMenuDeleteListItem");
                deleteItem.off();

                // Manually uploaded items have the naming convention "<IssueNumber> upload <Timestamp>"
                // Only those should be deletable
                if (isManualUpload(key)) {
                    deleteItem.css({display: "block"})

                    deleteItem.on('click', () => {
                        if (confirm(/*[[#{tab.contextMenu.delete.confirmation}]]*/)) {
                            const form = $('#scan-delete-form')
                            const formData = new FormData(form[0])

                            $.post({
                                url: `${issueScanDeleteBaseUrl}/${key}/delete`,
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function (result) {
                                    location.reload()
                                },
                                error: function (request, msg, error) {
                                    console.error(error)
                                    $('#scan-delete-error').fadeIn()
                                }
                            });
                        }
                    });

                } else {
                    deleteItem.css({display: "none"});
                    openItem.css({borderBottom: "none"});
                }

                $("#contextMenu").css(
                    {
                        display: 'block',
                        left: event.pageX + "px",
                        top: event.pageY + "px"
                    }
                )
            }
        }

        $.get(issueScanUrl + '/keys', function (keys, textStatus, jqXHR) {
            keys.forEach(key => {
                var link = $("<a></a>")
                    .attr('href', issueRetrieveUrl + key)
                    .attr('target', '_blank')
                    .addClass(isManualUpload(key) ? 'scan-manual-active' : 'scan-active')
                    .contextmenu(function (event) {
                        event.preventDefault();
                        showIssueScanContextMenu(event, issueRetrieveUrl, key);
                    });

                $('#issuescan').append(link)
            })
        });

        const uploadField = $('#scan-upload')
        $('#scan-upload-button').on('click', () => {
            uploadField.click()
        })

        uploadField.on('change', () => {
            const form = $('#scan-form')
            const formData = new FormData(form[0])
            $.post({
                url: issueScanUrl,
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => location.reload(),
                error: (error) => {
                    console.error(error)
                    $('#scan-error').fadeIn()
                }
            })
        })

        $('#note-form-submit-button').on('click', function () {
            $('#note-form').submit();
        });

        $('#recurring-issue-form-submit-button').on('click', function () {
            $('#recurring-issue-form').submit();
        });

        $('#assign-subissue-form-submit-button').on('click', function () {
            $('#assign-subissue-form').submit();
        });

        $('.subIssueAssignment').kendoComboBox({
            dataTextField: "externalId",
            dataValueField: "id",
            dataSource: new kendo.data.DataSource({
                serverPaging: false,
                serverSorting: false,
                serverFiltering: false,
                transport: {
                    read: {
                        url: baseURL + '/issues/assignable',
                        dataType: 'json',
                        data: {}
                    }
                },
                sortable: false,
                sort: {field: 'externalId', dir: 'asc'},
            }),
            filter: "contains",
            suggest: true,
            clearButton: false,
            noDataTemplate: /*[[#{errors.issue.notFound}]]*/,
        });

        $('#note-form-print-button').on('click', function () {
            window.location.href = "/admin/api/v1/print/printIssueDetail?issueId=" + issueId;
        });

        $('#delete-assignment-form').on('submit', function (e) {
            e.preventDefault()
            e.stopImmediatePropagation()

            var url = $(this).data("value")

            $.ajax({
                type: 'DELETE',
                url: url,
                data: $("#delete-assignment-form").serialize(),
                success: function (data) {
                    closePopupWindow()
                },
                error: function(error) {
                    $('#delete-error').fadeIn()
                }
            })
        });

        function closePopupWindow() {
            window.parent.document.getElementsByClassName('k-overlay')[0].click()
        }
    });

</script>
</body>
</html>
